/**
 * Serveur API Express pour la mémoire persistante Roony
 * Port: 8888
 * Endpoints: /api/memory/health, /api/memory/search, /api/memory/save
 */

import express from 'express';
import cors from 'cors';
import { spawn } from 'child_process';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';

// Configuration ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const PORT = 8888;

// Middleware
app.use(cors());
app.use(express.json());

// Configuration des chemins
const PROJECT_ROOT = __dirname;
const PYTHON_EXECUTABLE = path.join(PROJECT_ROOT, '.venv', 'Scripts', 'python.exe');
const MEMOIRE_DIR = path.join(PROJECT_ROOT, 'memoire');

// Logs avec timestamps
const log = (message) => {
  console.log(`[${new Date().toISOString()}] ${message}`);
};

// Vérification de l'environnement au démarrage
const checkEnvironment = () => {
  log('🔧 Vérification de l\'environnement...');
  
  // Vérification Python
  if (!fs.existsSync(PYTHON_EXECUTABLE)) {
    log(`❌ Python introuvable: ${PYTHON_EXECUTABLE}`);
    return false;
  }
  log(`✅ Python trouvé: ${PYTHON_EXECUTABLE}`);
  
  // Vérification répertoire mémoire
  if (!fs.existsSync(MEMOIRE_DIR)) {
    log(`❌ Répertoire mémoire introuvable: ${MEMOIRE_DIR}`);
    return false;
  }
  log(`✅ Répertoire mémoire trouvé: ${MEMOIRE_DIR}`);
  
  return true;
};

// Fonction utilitaire pour exécuter Python
const executePython = (script, args = []) => {
  return new Promise((resolve, reject) => {
    const pythonProcess = spawn(PYTHON_EXECUTABLE, [script, ...args], {
      cwd: PROJECT_ROOT,
      stdio: ['pipe', 'pipe', 'pipe']
    });
    
    let stdout = '';
    let stderr = '';
    
    pythonProcess.stdout.on('data', (data) => {
      stdout += data.toString();
    });
    
    pythonProcess.stderr.on('data', (data) => {
      stderr += data.toString();
    });
    
    pythonProcess.on('close', (code) => {
      if (code === 0) {
        resolve({ stdout, stderr });
      } else {
        reject(new Error(`Python process exited with code ${code}: ${stderr}`));
      }
    });
    
    // Timeout de 30 secondes
    setTimeout(() => {
      pythonProcess.kill();
      reject(new Error('Python process timeout'));
    }, 30000);
  });
};

// ENDPOINT: Health Check
app.get('/api/memory/health', async (req, res) => {
  log('🏥 Health check demandé');
  
  try {
    // Vérification rapide de l'environnement
    if (!checkEnvironment()) {
      return res.status(503).json({
        status: 'error',
        message: 'Environnement non configuré'
      });
    }
    
    // Test rapide du système Python
    const result = await executePython('-c', ['print("OK")']);
    
    log('✅ Health check réussi');
    res.json({
      status: 'healthy',
      message: 'Service mémoire opérationnel',
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    log(`❌ Health check échoué: ${error.message}`);
    res.status(503).json({
      status: 'error',
      message: 'Service mémoire indisponible',
      error: error.message
    });
  }
});

// ENDPOINT: Recherche dans la mémoire
app.post('/api/memory/search', async (req, res) => {
  const { query } = req.body;
  
  if (!query) {
    return res.status(400).json({
      error: 'Query manquante'
    });
  }
  
  log(`🔍 Recherche mémoire: "${query.substring(0, 50)}..."`);
  
  try {
    // Exécution du script de recherche Python
    const result = await executePython('recherche_memoire.py', [query]);
    
    // Parse du résultat JSON
    let searchResult;
    try {
      searchResult = JSON.parse(result.stdout);
    } catch (parseError) {
      log(`❌ Erreur parsing JSON: ${parseError.message}`);
      return res.status(500).json({
        error: 'Erreur de parsing des résultats'
      });
    }
    
    log(`✅ Recherche terminée: ${searchResult.cas_similaires?.length || 0} cas trouvés`);
    
    res.json(searchResult);
    
  } catch (error) {
    log(`❌ Erreur recherche: ${error.message}`);
    res.status(500).json({
      error: 'Erreur lors de la recherche',
      message: error.message
    });
  }
});

// ENDPOINT: Sauvegarde d'un nouveau cas
app.post('/api/memory/save', async (req, res) => {
  const cas = req.body;
  
  if (!cas || !cas.contexte || !cas.analyse) {
    return res.status(400).json({
      error: 'Données du cas incomplètes'
    });
  }
  
  log('💾 Sauvegarde d\'un nouveau cas...');
  
  try {
    // Génération d'un nom de fichier unique
    const timestamp = Date.now();
    const filename = `cas_${timestamp}.json`;
    const filepath = path.join(MEMOIRE_DIR, filename);
    
    // Ajout du timestamp au cas
    const casAvecTimestamp = {
      ...cas,
      timestamp: new Date().toISOString(),
      id: timestamp
    };
    
    // Sauvegarde du fichier JSON
    fs.writeFileSync(filepath, JSON.stringify(casAvecTimestamp, null, 2), 'utf8');
    
    log(`✅ Cas sauvegardé: ${filename}`);
    
    // Optionnel: Ré-indexation automatique
    try {
      await executePython('indexer_memoire.py');
      log('✅ Ré-indexation automatique terminée');
    } catch (indexError) {
      log(`⚠️ Ré-indexation échouée: ${indexError.message}`);
    }
    
    res.json({
      success: true,
      message: 'Cas sauvegardé avec succès',
      filename: filename,
      id: timestamp
    });
    
  } catch (error) {
    log(`❌ Erreur sauvegarde: ${error.message}`);
    res.status(500).json({
      error: 'Erreur lors de la sauvegarde',
      message: error.message
    });
  }
});

// ENDPOINT: Statistiques de la mémoire
app.get('/api/memory/stats', (req, res) => {
  log('📊 Statistiques demandées');
  
  try {
    if (!fs.existsSync(MEMOIRE_DIR)) {
      return res.json({
        nombre_cas_total: 0,
        derniere_indexation: 'Jamais',
        status: 'Non configuré'
      });
    }
    
    const fichiers = fs.readdirSync(MEMOIRE_DIR).filter(f => f.endsWith('.json'));
    
    res.json({
      nombre_cas_total: fichiers.length,
      derniere_indexation: new Date().toISOString(),
      status: 'Actif',
      fichiers: fichiers
    });
    
  } catch (error) {
    log(`❌ Erreur statistiques: ${error.message}`);
    res.status(500).json({
      error: 'Erreur lors de la récupération des statistiques',
      message: error.message
    });
  }
});

// Démarrage du serveur
const startServer = () => {
  if (!checkEnvironment()) {
    log('❌ Impossible de démarrer le serveur - environnement non configuré');
    process.exit(1);
  }
  
  app.listen(PORT, () => {
    log(`🚀 Serveur mémoire démarré sur http://localhost:${PORT}`);
    log('📋 Endpoints disponibles:');
    log('   GET  /api/memory/health - Health check');
    log('   POST /api/memory/search - Recherche dans la mémoire');
    log('   POST /api/memory/save   - Sauvegarde d\'un cas');
    log('   GET  /api/memory/stats  - Statistiques');
  });
};

// Gestion des erreurs non capturées
process.on('uncaughtException', (error) => {
  log(`💥 Erreur non capturée: ${error.message}`);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  log(`💥 Promesse rejetée: ${reason}`);
  process.exit(1);
});

// Démarrage
startServer();
